# API使用示例

这个文档提供了详细的API使用示例，帮助前端开发者快速上手。

## 基础URL

```
http://localhost:8080
```

## 认证流程

### 1. 用户注册

```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "Password123!"
  }'
```

**响应示例：**
```json
{
  "message": "用户注册成功",
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "created_at": "2025-08-25T11:00:00Z",
      "updated_at": "2025-08-25T11:00:00Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 2. 用户登录

```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "Password123!"
  }'
```

**响应示例：**
```json
{
  "message": "登录成功",
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "created_at": "2025-08-25T11:00:00Z",
      "updated_at": "2025-08-25T11:00:00Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 3. 获取用户资料

```bash
curl -X GET http://localhost:8080/api/auth/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 数据项操作

### 1. 创建数据项

```bash
curl -X POST http://localhost:8080/api/items \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "title": "我的第一个数据项",
    "description": "这是一个测试数据项的描述"
  }'
```

**响应示例：**
```json
{
  "message": "数据项创建成功",
  "data": {
    "id": 1,
    "title": "我的第一个数据项",
    "description": "这是一个测试数据项的描述",
    "user_id": 1,
    "created_at": "2025-08-25T11:00:00Z",
    "updated_at": "2025-08-25T11:00:00Z"
  }
}
```

### 2. 获取数据项列表

```bash
# 基础查询
curl -X GET http://localhost:8080/api/items \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 分页查询
curl -X GET "http://localhost:8080/api/items?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 搜索查询
curl -X GET "http://localhost:8080/api/items?search=测试" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**响应示例：**
```json
{
  "message": "获取数据项列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "title": "我的第一个数据项",
        "description": "这是一个测试数据项的描述",
        "user_id": 1,
        "created_at": "2025-08-25T11:00:00Z",
        "updated_at": "2025-08-25T11:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 1
    }
  }
}
```

### 3. 获取单个数据项

```bash
curl -X GET http://localhost:8080/api/items/1 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 4. 更新数据项

```bash
curl -X PUT http://localhost:8080/api/items/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "title": "更新后的标题",
    "description": "更新后的描述"
  }'
```

### 5. 删除数据项

```bash
curl -X DELETE http://localhost:8080/api/items/1 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## JavaScript/前端示例

### 使用Fetch API

```javascript
// 基础配置
const API_BASE_URL = 'http://localhost:8080';
let authToken = localStorage.getItem('authToken');

// 通用请求函数
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE_URL}${endpoint}`;
  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  if (authToken) {
    config.headers.Authorization = `Bearer ${authToken}`;
  }

  const response = await fetch(url, config);
  const data = await response.json();

  if (!response.ok) {
    throw new Error(data.message || '请求失败');
  }

  return data;
}

// 用户注册
async function register(username, email, password) {
  const data = await apiRequest('/api/auth/register', {
    method: 'POST',
    body: JSON.stringify({ username, email, password }),
  });
  
  authToken = data.data.token;
  localStorage.setItem('authToken', authToken);
  return data;
}

// 用户登录
async function login(username, password) {
  const data = await apiRequest('/api/auth/login', {
    method: 'POST',
    body: JSON.stringify({ username, password }),
  });
  
  authToken = data.data.token;
  localStorage.setItem('authToken', authToken);
  return data;
}

// 获取数据项列表
async function getItems(page = 1, limit = 10, search = '') {
  const params = new URLSearchParams({ page, limit });
  if (search) params.append('search', search);
  
  return await apiRequest(`/api/items?${params}`);
}

// 创建数据项
async function createItem(title, description) {
  return await apiRequest('/api/items', {
    method: 'POST',
    body: JSON.stringify({ title, description }),
  });
}

// 更新数据项
async function updateItem(id, title, description) {
  return await apiRequest(`/api/items/${id}`, {
    method: 'PUT',
    body: JSON.stringify({ title, description }),
  });
}

// 删除数据项
async function deleteItem(id) {
  return await apiRequest(`/api/items/${id}`, {
    method: 'DELETE',
  });
}
```

## 错误处理

API返回的错误格式统一为：

```json
{
  "error": "错误类型",
  "message": "详细错误信息",
  "code": 400
}
```

常见错误码：
- `400` - 请求参数错误
- `401` - 未授权（需要登录）
- `403` - 权限不足
- `404` - 资源不存在
- `409` - 资源冲突（如用户名已存在）
- `500` - 服务器内部错误

## 健康检查

```bash
curl -X GET http://localhost:8080/health
```

**响应示例：**
```json
{
  "status": "ok",
  "message": "服务运行正常",
  "time": "2025-08-25 11:00:00"
}
```
