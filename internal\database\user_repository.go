package database

import (
	"database/sql"
	"fmt"
	"time"

	"golang-learning-mvp/internal/models"
)

// UserRepository 用户数据访问层
// 这个结构体封装了所有与用户相关的数据库操作
type UserRepository struct {
	db *sql.DB
}

// NewUserRepository 创建用户仓库实例
// 这是Go中常见的构造函数模式
func NewUserRepository(db *sql.DB) *UserRepository {
	return &UserRepository{db: db}
}

// CreateUser 创建新用户
// 这个方法展示了Go中的数据库插入操作
func (r *UserRepository) CreateUser(user *models.User) error {
	query := `
		INSERT INTO users (username, email, password, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?)
	`
	
	now := time.Now()
	user.CreatedAt = now
	user.UpdatedAt = now

	// 执行插入操作并获取插入的ID
	result, err := r.db.Exec(query, user.Username, user.Email, user.Password, now, now)
	if err != nil {
		return fmt.Errorf("创建用户失败: %v", err)
	}

	// 获取自动生成的ID
	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取用户ID失败: %v", err)
	}

	user.ID = int(id)
	return nil
}

// GetUserByID 根据ID获取用户
// 这个方法展示了Go中的数据库查询操作
func (r *UserRepository) GetUserByID(id int) (*models.User, error) {
	query := `
		SELECT id, username, email, password, created_at, updated_at
		FROM users WHERE id = ?
	`

	user := &models.User{}
	err := r.db.QueryRow(query, id).Scan(
		&user.ID,
		&user.Username,
		&user.Email,
		&user.Password,
		&user.CreatedAt,
		&user.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("用户不存在")
		}
		return nil, fmt.Errorf("查询用户失败: %v", err)
	}

	return user, nil
}

// GetUserByUsername 根据用户名获取用户
// 这个方法用于登录验证
func (r *UserRepository) GetUserByUsername(username string) (*models.User, error) {
	query := `
		SELECT id, username, email, password, created_at, updated_at
		FROM users WHERE username = ?
	`

	user := &models.User{}
	err := r.db.QueryRow(query, username).Scan(
		&user.ID,
		&user.Username,
		&user.Email,
		&user.Password,
		&user.CreatedAt,
		&user.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("用户不存在")
		}
		return nil, fmt.Errorf("查询用户失败: %v", err)
	}

	return user, nil
}

// GetUserByEmail 根据邮箱获取用户
func (r *UserRepository) GetUserByEmail(email string) (*models.User, error) {
	query := `
		SELECT id, username, email, password, created_at, updated_at
		FROM users WHERE email = ?
	`

	user := &models.User{}
	err := r.db.QueryRow(query, email).Scan(
		&user.ID,
		&user.Username,
		&user.Email,
		&user.Password,
		&user.CreatedAt,
		&user.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("用户不存在")
		}
		return nil, fmt.Errorf("查询用户失败: %v", err)
	}

	return user, nil
}

// UpdateUser 更新用户信息
func (r *UserRepository) UpdateUser(user *models.User) error {
	query := `
		UPDATE users 
		SET username = ?, email = ?, updated_at = ?
		WHERE id = ?
	`

	user.UpdatedAt = time.Now()
	
	result, err := r.db.Exec(query, user.Username, user.Email, user.UpdatedAt, user.ID)
	if err != nil {
		return fmt.Errorf("更新用户失败: %v", err)
	}

	// 检查是否有行被更新
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("检查更新结果失败: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("用户不存在或无更新")
	}

	return nil
}

// DeleteUser 删除用户
func (r *UserRepository) DeleteUser(id int) error {
	query := `DELETE FROM users WHERE id = ?`
	
	result, err := r.db.Exec(query, id)
	if err != nil {
		return fmt.Errorf("删除用户失败: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("检查删除结果失败: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("用户不存在")
	}

	return nil
}

// CheckUserExists 检查用户名或邮箱是否已存在
// 这个方法用于注册时的重复检查
func (r *UserRepository) CheckUserExists(username, email string) (bool, error) {
	query := `
		SELECT COUNT(*) FROM users 
		WHERE username = ? OR email = ?
	`

	var count int
	err := r.db.QueryRow(query, username, email).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("检查用户存在性失败: %v", err)
	}

	return count > 0, nil
}
