package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"golang-learning-mvp/internal/auth"
	"golang-learning-mvp/internal/database"
	"golang-learning-mvp/internal/middleware"
	"golang-learning-mvp/internal/models"
)

// AuthHandler 认证处理器
// 这个结构体包含认证相关的所有处理方法
type AuthHandler struct {
	userRepo       *database.UserRepository // 用户数据访问层
	jwtManager     *auth.JWTManager         // JWT管理器
	passwordManager *auth.PasswordManager    // 密码管理器
}

// NewAuthHandler 创建认证处理器实例
func NewAuthHandler(userRepo *database.UserRepository, jwtManager *auth.JWTManager, passwordManager *auth.PasswordManager) *AuthHandler {
	return &AuthHandler{
		userRepo:       userRepo,
		jwtManager:     jwtManager,
		passwordManager: passwordManager,
	}
}

// Register 用户注册处理器
// @Summary 用户注册
// @Description 创建新用户账户
// @Tags 认证
// @Accept json
// @Produce json
// @Param user body models.UserCreateRequest true "用户注册信息"
// @Success 201 {object} middleware.SuccessResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 409 {object} middleware.ErrorResponse
// @Router /api/auth/register [post]
func (h *AuthHandler) Register(c *gin.Context) {
	var req models.UserCreateRequest
	
	// 绑定JSON数据到结构体
	// Gin会自动验证binding标签中的规则
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.HandleBadRequestError(c, "请求数据格式错误: "+err.Error())
		return
	}

	// 检查密码强度（可选功能）
	if strong, issues := h.passwordManager.IsPasswordStrong(req.Password); !strong {
		middleware.HandleBadRequestError(c, "密码强度不足: "+issues[0])
		return
	}

	// 检查用户是否已存在
	exists, err := h.userRepo.CheckUserExists(req.Username, req.Email)
	if err != nil {
		middleware.HandleDatabaseError(c, err)
		return
	}
	if exists {
		c.JSON(http.StatusConflict, middleware.ErrorResponse{
			Error:   "用户已存在",
			Message: "用户名或邮箱已被使用",
			Code:    409,
		})
		return
	}

	// 哈希密码
	hashedPassword, err := h.passwordManager.HashPassword(req.Password)
	if err != nil {
		middleware.HandleDatabaseError(c, err)
		return
	}

	// 创建用户模型
	user := &models.User{
		Username: req.Username,
		Email:    req.Email,
		Password: hashedPassword,
	}

	// 保存用户到数据库
	if err := h.userRepo.CreateUser(user); err != nil {
		middleware.HandleDatabaseError(c, err)
		return
	}

	// 生成JWT token
	token, err := h.jwtManager.GenerateToken(user.ID, user.Username)
	if err != nil {
		middleware.HandleDatabaseError(c, err)
		return
	}

	// 返回成功响应
	middleware.HandleCreated(c, "用户注册成功", gin.H{
		"user":  user.ToResponse(),
		"token": token,
	})
}

// Login 用户登录处理器
// @Summary 用户登录
// @Description 用户身份验证
// @Tags 认证
// @Accept json
// @Produce json
// @Param credentials body models.UserLoginRequest true "登录凭据"
// @Success 200 {object} middleware.SuccessResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Router /api/auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
	var req models.UserLoginRequest
	
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.HandleBadRequestError(c, "请求数据格式错误: "+err.Error())
		return
	}

	// 根据用户名查找用户
	user, err := h.userRepo.GetUserByUsername(req.Username)
	if err != nil {
		middleware.HandleAuthError(c, "用户名或密码错误")
		return
	}

	// 验证密码
	if err := h.passwordManager.VerifyPassword(user.Password, req.Password); err != nil {
		middleware.HandleAuthError(c, "用户名或密码错误")
		return
	}

	// 生成JWT token
	token, err := h.jwtManager.GenerateToken(user.ID, user.Username)
	if err != nil {
		middleware.HandleDatabaseError(c, err)
		return
	}

	// 返回成功响应
	middleware.HandleSuccess(c, "登录成功", gin.H{
		"user":  user.ToResponse(),
		"token": token,
	})
}

// RefreshToken 刷新token处理器
// @Summary 刷新访问令牌
// @Description 使用当前token获取新的token
// @Tags 认证
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} middleware.SuccessResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Router /api/auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	// 从Authorization头获取当前token
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		middleware.HandleAuthError(c, "缺少Authorization头")
		return
	}

	// 提取token
	tokenString := authHeader[7:] // 移除"Bearer "前缀
	
	// 刷新token
	newToken, err := h.jwtManager.RefreshToken(tokenString)
	if err != nil {
		middleware.HandleAuthError(c, "无法刷新token: "+err.Error())
		return
	}

	middleware.HandleSuccess(c, "Token刷新成功", gin.H{
		"token": newToken,
	})
}

// GetProfile 获取用户资料处理器
// @Summary 获取当前用户资料
// @Description 获取已认证用户的个人信息
// @Tags 用户
// @Produce json
// @Security BearerAuth
// @Success 200 {object} middleware.SuccessResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Router /api/auth/profile [get]
func (h *AuthHandler) GetProfile(c *gin.Context) {
	// 从中间件获取用户ID
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		middleware.HandleAuthError(c, "无法获取用户信息")
		return
	}

	// 查询用户信息
	user, err := h.userRepo.GetUserByID(userID)
	if err != nil {
		middleware.HandleDatabaseError(c, err)
		return
	}

	middleware.HandleSuccess(c, "获取用户资料成功", user.ToResponse())
}

// Logout 用户登出处理器
// @Summary 用户登出
// @Description 用户登出（客户端需要删除token）
// @Tags 认证
// @Produce json
// @Security BearerAuth
// @Success 200 {object} middleware.SuccessResponse
// @Router /api/auth/logout [post]
func (h *AuthHandler) Logout(c *gin.Context) {
	// 在无状态JWT系统中，登出主要是客户端行为
	// 服务端可以记录登出事件或将token加入黑名单（高级功能）
	
	middleware.HandleSuccess(c, "登出成功", gin.H{
		"message": "请在客户端删除存储的token",
	})
}
