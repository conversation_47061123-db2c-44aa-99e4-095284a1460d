package middleware

import (
	"fmt"
	"log"
	"time"

	"github.com/gin-gonic/gin"
)

// LoggerMiddleware 自定义日志中间件
// 这个中间件记录每个HTTP请求的详细信息
func LoggerMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// 自定义日志格式
		return fmt.Sprintf("[%s] %s %s %s %d %s %s\n",
			param.TimeStamp.Format("2006-01-02 15:04:05"), // 时间戳
			param.ClientIP,                                  // 客户端IP
			param.Method,                                    // HTTP方法
			param.Path,                                      // 请求路径
			param.StatusCode,                                // 状态码
			param.Latency,                                   // 响应时间
			param.ErrorMessage,                              // 错误信息
		)
	})
}

// DetailedLoggerMiddleware 详细日志中间件
// 这个中间件提供更详细的请求日志记录
func DetailedLoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录请求开始时间
		startTime := time.Now()

		// 记录请求信息
		log.Printf("开始处理请求: %s %s from %s", c.Request.Method, c.Request.URL.Path, c.ClientIP())

		// 处理请求
		c.Next()

		// 计算处理时间
		latency := time.Since(startTime)

		// 记录响应信息
		statusCode := c.Writer.Status()
		
		// 根据状态码选择日志级别
		if statusCode >= 400 {
			log.Printf("请求完成 [错误]: %s %s - 状态码: %d, 耗时: %v, 客户端: %s",
				c.Request.Method, c.Request.URL.Path, statusCode, latency, c.ClientIP())
		} else {
			log.Printf("请求完成 [成功]: %s %s - 状态码: %d, 耗时: %v, 客户端: %s",
				c.Request.Method, c.Request.URL.Path, statusCode, latency, c.ClientIP())
		}
	}
}

// RequestIDMiddleware 请求ID中间件
// 这个中间件为每个请求生成唯一ID，便于追踪
func RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 生成请求ID（简单实现，生产环境可能需要更复杂的ID生成策略）
		requestID := fmt.Sprintf("%d", time.Now().UnixNano())
		
		// 设置请求ID到上下文
		c.Set("request_id", requestID)
		
		// 设置响应头
		c.Header("X-Request-ID", requestID)
		
		// 记录请求开始
		log.Printf("[%s] 请求开始: %s %s", requestID, c.Request.Method, c.Request.URL.Path)
		
		c.Next()
		
		// 记录请求结束
		log.Printf("[%s] 请求结束: 状态码 %d", requestID, c.Writer.Status())
	}
}

// GetRequestIDFromContext 从上下文获取请求ID
func GetRequestIDFromContext(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}

// APILoggerMiddleware API专用日志中间件
// 这个中间件专门为API请求设计，记录更多API相关信息
func APILoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()
		
		// 获取用户信息（如果已认证）
		userID, hasUser := c.Get("user_id")
		username, hasUsername := c.Get("username")
		
		// 记录请求开始
		if hasUser && hasUsername {
			log.Printf("API请求开始: %s %s - 用户: %v(%v), IP: %s",
				c.Request.Method, c.Request.URL.Path, username, userID, c.ClientIP())
		} else {
			log.Printf("API请求开始: %s %s - 匿名用户, IP: %s",
				c.Request.Method, c.Request.URL.Path, c.ClientIP())
		}

		c.Next()

		// 记录请求结束
		latency := time.Since(startTime)
		statusCode := c.Writer.Status()
		
		logLevel := "INFO"
		if statusCode >= 400 && statusCode < 500 {
			logLevel = "WARN"
		} else if statusCode >= 500 {
			logLevel = "ERROR"
		}
		
		log.Printf("[%s] API请求完成: %s %s - 状态码: %d, 耗时: %v",
			logLevel, c.Request.Method, c.Request.URL.Path, statusCode, latency)
	}
}
