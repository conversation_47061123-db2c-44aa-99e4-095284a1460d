package auth

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// Claims JWT声明结构体
// 这个结构体定义了JWT token中包含的信息
type Claims struct {
	UserID   int    `json:"user_id"`   // 用户ID
	Username string `json:"username"`  // 用户名
	jwt.RegisteredClaims                // 嵌入标准声明
}

// JWTManager JWT管理器
// 这个结构体封装了JWT相关的操作
type JWTManager struct {
	secretKey []byte        // JWT签名密钥
	issuer    string        // 发行者
	expiry    time.Duration // 过期时间
}

// NewJWTManager 创建JWT管理器实例
// 这是Go中常见的构造函数模式
func NewJWTManager(secretKey, issuer string, expiry time.Duration) *JWTManager {
	return &JWTManager{
		secretKey: []byte(secretKey),
		issuer:    issuer,
		expiry:    expiry,
	}
}

// GenerateToken 生成JWT token
// 这个方法为用户生成访问令牌
func (j *JWTManager) GenerateToken(userID int, username string) (string, error) {
	// 创建声明
	claims := Claims{
		UserID:   userID,
		Username: username,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    j.issuer,                                // 发行者
			Subject:   fmt.Sprintf("%d", userID),               // 主题（用户ID）
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(j.expiry)), // 过期时间
			IssuedAt:  jwt.NewNumericDate(time.Now()),          // 发行时间
			NotBefore: jwt.NewNumericDate(time.Now()),          // 生效时间
		},
	}

	// 创建token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 签名token
	tokenString, err := token.SignedString(j.secretKey)
	if err != nil {
		return "", fmt.Errorf("生成token失败: %v", err)
	}

	return tokenString, nil
}

// ValidateToken 验证JWT token
// 这个方法验证token的有效性并返回声明信息
func (j *JWTManager) ValidateToken(tokenString string) (*Claims, error) {
	// 解析token
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("意外的签名方法: %v", token.Header["alg"])
		}
		return j.secretKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("解析token失败: %v", err)
	}

	// 验证token有效性
	if !token.Valid {
		return nil, fmt.Errorf("无效的token")
	}

	// 提取声明
	claims, ok := token.Claims.(*Claims)
	if !ok {
		return nil, fmt.Errorf("无法提取token声明")
	}

	return claims, nil
}

// RefreshToken 刷新token
// 这个方法为即将过期的token生成新的token
func (j *JWTManager) RefreshToken(tokenString string) (string, error) {
	// 首先验证当前token
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return "", fmt.Errorf("无法刷新无效token: %v", err)
	}

	// 检查token是否在刷新窗口内（例如：过期前30分钟）
	refreshWindow := 30 * time.Minute
	if time.Until(claims.ExpiresAt.Time) > refreshWindow {
		return "", fmt.Errorf("token还未到刷新时间")
	}

	// 生成新token
	return j.GenerateToken(claims.UserID, claims.Username)
}

// ExtractUserIDFromToken 从token中提取用户ID
// 这是一个便利方法，用于快速获取用户ID
func (j *JWTManager) ExtractUserIDFromToken(tokenString string) (int, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return 0, err
	}
	return claims.UserID, nil
}

// ExtractUsernameFromToken 从token中提取用户名
func (j *JWTManager) ExtractUsernameFromToken(tokenString string) (string, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return "", err
	}
	return claims.Username, nil
}

// IsTokenExpired 检查token是否过期
func (j *JWTManager) IsTokenExpired(tokenString string) bool {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return true // 如果无法验证，认为已过期
	}
	return time.Now().After(claims.ExpiresAt.Time)
}
