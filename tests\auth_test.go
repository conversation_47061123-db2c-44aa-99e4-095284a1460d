package tests

import (
	"testing"
	"time"

	"golang-learning-mvp/internal/auth"
)

// TestPasswordManager 测试密码管理器
func TestPasswordManager(t *testing.T) {
	pm := auth.NewPasswordManager()

	// 测试密码哈希
	t.Run("密码哈希测试", func(t *testing.T) {
		password := "testpassword123"
		
		// 哈希密码
		hashedPassword, err := pm.HashPassword(password)
		if err != nil {
			t.Fatalf("密码哈希失败: %v", err)
		}

		// 验证哈希后的密码不等于原密码
		if hashedPassword == password {
			t.Error("哈希后的密码不应该等于原密码")
		}

		// 验证密码
		err = pm.VerifyPassword(hashedPassword, password)
		if err != nil {
			t.Fatalf("密码验证失败: %v", err)
		}

		// 验证错误密码
		err = pm.VerifyPassword(hashedPassword, "wrongpassword")
		if err == nil {
			t.Error("错误密码应该验证失败")
		}
	})

	// 测试空密码
	t.Run("空密码测试", func(t *testing.T) {
		_, err := pm.HashPassword("")
		if err == nil {
			t.Error("空密码应该返回错误")
		}

		err = pm.VerifyPassword("hash", "")
		if err == nil {
			t.Error("空密码验证应该失败")
		}
	})

	// 测试密码强度检查
	t.Run("密码强度测试", func(t *testing.T) {
		testCases := []struct {
			password string
			expected bool
		}{
			{"weak", false},           // 太短
			{"password", false},       // 缺少数字和大写字母
			{"Password", false},       // 缺少数字
			{"Password1", false},      // 缺少特殊字符
			{"Password1!", true},      // 强密码
		}

		for _, tc := range testCases {
			strong, _ := pm.IsPasswordStrong(tc.password)
			if strong != tc.expected {
				t.Errorf("密码 '%s' 强度检查失败，期望: %v, 实际: %v", tc.password, tc.expected, strong)
			}
		}
	})
}

// TestJWTManager 测试JWT管理器
func TestJWTManager(t *testing.T) {
	secretKey := "test-secret-key"
	issuer := "test-issuer"
	expiry := time.Hour

	jwtManager := auth.NewJWTManager(secretKey, issuer, expiry)

	userID := 123
	username := "testuser"

	// 测试token生成
	t.Run("Token生成测试", func(t *testing.T) {
		token, err := jwtManager.GenerateToken(userID, username)
		if err != nil {
			t.Fatalf("生成token失败: %v", err)
		}

		if token == "" {
			t.Error("生成的token不应该为空")
		}
	})

	// 测试token验证
	t.Run("Token验证测试", func(t *testing.T) {
		// 生成token
		token, err := jwtManager.GenerateToken(userID, username)
		if err != nil {
			t.Fatalf("生成token失败: %v", err)
		}

		// 验证token
		claims, err := jwtManager.ValidateToken(token)
		if err != nil {
			t.Fatalf("验证token失败: %v", err)
		}

		// 检查声明
		if claims.UserID != userID {
			t.Errorf("用户ID不匹配，期望: %d, 实际: %d", userID, claims.UserID)
		}

		if claims.Username != username {
			t.Errorf("用户名不匹配，期望: %s, 实际: %s", username, claims.Username)
		}
	})

	// 测试无效token
	t.Run("无效Token测试", func(t *testing.T) {
		invalidToken := "invalid.token.here"
		
		_, err := jwtManager.ValidateToken(invalidToken)
		if err == nil {
			t.Error("无效token应该验证失败")
		}
	})

	// 测试提取用户信息
	t.Run("提取用户信息测试", func(t *testing.T) {
		token, err := jwtManager.GenerateToken(userID, username)
		if err != nil {
			t.Fatalf("生成token失败: %v", err)
		}

		// 提取用户ID
		extractedUserID, err := jwtManager.ExtractUserIDFromToken(token)
		if err != nil {
			t.Fatalf("提取用户ID失败: %v", err)
		}

		if extractedUserID != userID {
			t.Errorf("提取的用户ID不匹配，期望: %d, 实际: %d", userID, extractedUserID)
		}

		// 提取用户名
		extractedUsername, err := jwtManager.ExtractUsernameFromToken(token)
		if err != nil {
			t.Fatalf("提取用户名失败: %v", err)
		}

		if extractedUsername != username {
			t.Errorf("提取的用户名不匹配，期望: %s, 实际: %s", username, extractedUsername)
		}
	})
}

// BenchmarkPasswordHashing 密码哈希性能测试
func BenchmarkPasswordHashing(b *testing.B) {
	pm := auth.NewPasswordManager()
	password := "testpassword123"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := pm.HashPassword(password)
		if err != nil {
			b.Fatalf("密码哈希失败: %v", err)
		}
	}
}

// BenchmarkTokenGeneration Token生成性能测试
func BenchmarkTokenGeneration(b *testing.B) {
	jwtManager := auth.NewJWTManager("test-secret", "test-issuer", time.Hour)
	userID := 123
	username := "testuser"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := jwtManager.GenerateToken(userID, username)
		if err != nil {
			b.Fatalf("生成token失败: %v", err)
		}
	}
}
