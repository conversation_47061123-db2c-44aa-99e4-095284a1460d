package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// CORSMiddleware CORS跨域中间件
// 这个中间件处理跨域资源共享，允许前端应用访问API
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置CORS响应头
		c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*") // 允许所有域名访问
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON>er("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.<PERSON><PERSON>("Access-Control-Expose-Headers", "Content-Length")
		c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")

		// 处理预检请求
		// 浏览器在发送跨域请求前会先发送OPTIONS请求进行预检
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// CORSMiddlewareWithConfig 带配置的CORS中间件
// 这个版本允许更精细的CORS配置
func CORSMiddlewareWithConfig(allowedOrigins []string, allowedMethods []string, allowedHeaders []string) gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		
		// 检查是否允许该域名
		allowed := false
		for _, allowedOrigin := range allowedOrigins {
			if allowedOrigin == "*" || allowedOrigin == origin {
				allowed = true
				break
			}
		}

		if allowed {
			c.Header("Access-Control-Allow-Origin", origin)
		}

		// 设置允许的方法
		methods := "GET, POST, PUT, DELETE, OPTIONS"
		if len(allowedMethods) > 0 {
			methods = ""
			for i, method := range allowedMethods {
				if i > 0 {
					methods += ", "
				}
				methods += method
			}
		}
		c.Header("Access-Control-Allow-Methods", methods)

		// 设置允许的头部
		headers := "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization"
		if len(allowedHeaders) > 0 {
			headers = ""
			for i, header := range allowedHeaders {
				if i > 0 {
					headers += ", "
				}
				headers += header
			}
		}
		c.Header("Access-Control-Allow-Headers", headers)

		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// SecurityHeadersMiddleware 安全头中间件
// 这个中间件添加常见的安全响应头
func SecurityHeadersMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 防止点击劫持
		c.Header("X-Frame-Options", "DENY")
		
		// 防止MIME类型嗅探
		c.Header("X-Content-Type-Options", "nosniff")
		
		// XSS保护
		c.Header("X-XSS-Protection", "1; mode=block")
		
		// 强制HTTPS（在生产环境中启用）
		// c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		
		// 内容安全策略（根据需要配置）
		// c.Header("Content-Security-Policy", "default-src 'self'")
		
		// 引用者策略
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")

		c.Next()
	}
}
