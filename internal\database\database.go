package database

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"path/filepath"

	_ "modernc.org/sqlite" // 纯Go SQLite驱动，使用空白导入
)

// DB 数据库连接实例
// 在Go中，通常使用全局变量来存储数据库连接
var DB *sql.DB

// InitDatabase 初始化数据库连接
// 这个函数展示了Go中数据库初始化的标准模式
func InitDatabase(databasePath string) error {
	// 确保数据库目录存在
	dir := filepath.Dir(databasePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建数据库目录失败: %v", err)
	}

	// 打开数据库连接
	// sql.Open不会立即连接数据库，只是准备数据库连接
	var err error
	DB, err = sql.Open("sqlite", databasePath)
	if err != nil {
		return fmt.Errorf("打开数据库失败: %v", err)
	}

	// 测试数据库连接
	// Ping方法会实际连接数据库
	if err = DB.Ping(); err != nil {
		return fmt.Errorf("连接数据库失败: %v", err)
	}

	log.Printf("数据库连接成功: %s", databasePath)

	// 创建表结构
	if err = createTables(); err != nil {
		return fmt.Errorf("创建表失败: %v", err)
	}

	return nil
}

// createTables 创建数据库表
// 这个函数定义了应用程序的数据库结构
func createTables() error {
	// 用户表SQL
	userTableSQL := `
	CREATE TABLE IF NOT EXISTS users (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		username TEXT UNIQUE NOT NULL,
		email TEXT UNIQUE NOT NULL,
		password TEXT NOT NULL,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);`

	// 数据项表SQL
	itemTableSQL := `
	CREATE TABLE IF NOT EXISTS items (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		title TEXT NOT NULL,
		description TEXT,
		user_id INTEGER NOT NULL,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
	);`

	// 创建索引以提高查询性能
	indexSQL := `
	CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
	CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
	CREATE INDEX IF NOT EXISTS idx_items_user_id ON items(user_id);
	CREATE INDEX IF NOT EXISTS idx_items_created_at ON items(created_at);
	`

	// 执行SQL语句
	// 在Go中，通常将多个SQL语句组合执行
	queries := []string{userTableSQL, itemTableSQL, indexSQL}
	
	for _, query := range queries {
		if _, err := DB.Exec(query); err != nil {
			return fmt.Errorf("执行SQL失败: %v, SQL: %s", err, query)
		}
	}

	log.Println("数据库表创建成功")
	return nil
}

// CloseDatabase 关闭数据库连接
// 这个函数通常在应用程序关闭时调用
func CloseDatabase() error {
	if DB != nil {
		log.Println("关闭数据库连接")
		return DB.Close()
	}
	return nil
}

// GetDB 获取数据库连接实例
// 这是一个常见的模式，用于在其他包中获取数据库连接
func GetDB() *sql.DB {
	return DB
}

// 数据库健康检查
func HealthCheck() error {
	if DB == nil {
		return fmt.Errorf("数据库连接未初始化")
	}
	return DB.Ping()
}
