package main

import (
	"log"
	"time"

	"github.com/gin-gonic/gin"
	"golang-learning-mvp/config"
	"golang-learning-mvp/internal/auth"
	"golang-learning-mvp/internal/database"
	"golang-learning-mvp/internal/handlers"
	"golang-learning-mvp/internal/middleware"
)

func main() {
	// 加载配置
	cfg := config.LoadConfig()

	// 初始化数据库
	if err := database.InitDatabase(cfg.DatabasePath); err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}
	defer database.CloseDatabase()

	// 创建仓库实例
	userRepo := database.NewUserRepository(database.GetDB())
	itemRepo := database.NewItemRepository(database.GetDB())

	// 创建认证管理器
	jwtManager := auth.NewJWTManager(cfg.JWTSecret, "golang-learning-mvp", 24*time.Hour)
	passwordManager := auth.NewPasswordManager()

	// 创建处理器
	authHandler := handlers.NewAuthHandler(userRepo, jwtManager, passwordManager)
	itemHandler := handlers.NewItemHandler(itemRepo)

	// 设置Gin模式
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建Gin路由器
	router := gin.New()

	// 添加全局中间件
	router.Use(middleware.LoggerMiddleware())           // 日志中间件
	router.Use(middleware.ErrorHandlerMiddleware())     // 错误处理中间件
	router.Use(middleware.CORSMiddleware())             // CORS中间件
	router.Use(middleware.SecurityHeadersMiddleware())  // 安全头中间件

	// 健康检查端点
	router.GET("/health", func(c *gin.Context) {
		if err := database.HealthCheck(); err != nil {
			c.JSON(500, gin.H{
				"status":   "error",
				"message":  "数据库连接失败",
				"error":    err.Error(),
			})
			return
		}
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "服务运行正常",
			"time":    time.Now().Format("2006-01-02 15:04:05"),
		})
	})

	// API路由组
	api := router.Group("/api")
	{
		// 认证路由（无需认证）
		auth := api.Group("/auth")
		{
			auth.POST("/register", authHandler.Register)
			auth.POST("/login", authHandler.Login)
			auth.POST("/refresh", authHandler.RefreshToken)
		}

		// 需要认证的路由
		authenticated := api.Group("")
		authenticated.Use(middleware.AuthMiddleware(jwtManager))
		{
			// 用户资料路由
			authenticated.GET("/auth/profile", authHandler.GetProfile)
			authenticated.POST("/auth/logout", authHandler.Logout)

			// 数据项路由
			items := authenticated.Group("/items")
			{
				items.POST("", itemHandler.CreateItem)
				items.GET("", itemHandler.GetItems)
				items.GET("/:id", itemHandler.GetItem)
				items.PUT("/:id", itemHandler.UpdateItem)
				items.DELETE("/:id", itemHandler.DeleteItem)
			}
		}
	}

	// 404处理
	router.NoRoute(middleware.NotFoundHandler())

	// 启动服务器
	log.Printf("服务器启动在端口 %s", cfg.Port)
	log.Printf("环境: %s", cfg.Environment)
	log.Printf("数据库: %s", cfg.DatabasePath)
	log.Println("API文档:")
	log.Println("  POST /api/auth/register - 用户注册")
	log.Println("  POST /api/auth/login - 用户登录")
	log.Println("  GET  /api/auth/profile - 获取用户资料")
	log.Println("  POST /api/items - 创建数据项")
	log.Println("  GET  /api/items - 获取数据项列表")
	log.Println("  GET  /api/items/:id - 获取数据项详情")
	log.Println("  PUT  /api/items/:id - 更新数据项")
	log.Println("  DELETE /api/items/:id - 删除数据项")
	log.Println("  GET  /health - 健康检查")

	if err := router.Run(":" + cfg.Port); err != nil {
		log.Fatalf("服务器启动失败: %v", err)
	}
}
