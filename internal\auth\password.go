package auth

import (
	"fmt"

	"golang.org/x/crypto/bcrypt"
)

// PasswordManager 密码管理器
// 这个结构体封装了密码相关的操作
type PasswordManager struct {
	cost int // bcrypt的成本参数，值越高越安全但越慢
}

// NewPasswordManager 创建密码管理器实例
func NewPasswordManager() *PasswordManager {
	return &PasswordManager{
		cost: bcrypt.DefaultCost, // 使用默认成本（通常是10）
	}
}

// HashPassword 对密码进行哈希处理
// 这个方法使用bcrypt算法对密码进行安全哈希
func (pm *PasswordManager) HashPassword(password string) (string, error) {
	// 输入验证
	if len(password) == 0 {
		return "", fmt.Errorf("密码不能为空")
	}

	// 使用bcrypt生成哈希
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), pm.cost)
	if err != nil {
		return "", fmt.Errorf("密码哈希失败: %v", err)
	}

	return string(hashedBytes), nil
}

// VerifyPassword 验证密码
// 这个方法比较明文密码和哈希密码是否匹配
func (pm *PasswordManager) VerifyPassword(hashedPassword, password string) error {
	// 输入验证
	if len(hashedPassword) == 0 || len(password) == 0 {
		return fmt.Errorf("密码和哈希值不能为空")
	}

	// 使用bcrypt比较密码
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
	if err != nil {
		if err == bcrypt.ErrMismatchedHashAndPassword {
			return fmt.Errorf("密码不正确")
		}
		return fmt.Errorf("密码验证失败: %v", err)
	}

	return nil
}

// IsPasswordStrong 检查密码强度
// 这个方法实现基本的密码强度检查
func (pm *PasswordManager) IsPasswordStrong(password string) (bool, []string) {
	var issues []string

	// 检查长度
	if len(password) < 8 {
		issues = append(issues, "密码长度至少8位")
	}

	// 检查是否包含数字
	hasDigit := false
	for _, char := range password {
		if char >= '0' && char <= '9' {
			hasDigit = true
			break
		}
	}
	if !hasDigit {
		issues = append(issues, "密码应包含至少一个数字")
	}

	// 检查是否包含小写字母
	hasLower := false
	for _, char := range password {
		if char >= 'a' && char <= 'z' {
			hasLower = true
			break
		}
	}
	if !hasLower {
		issues = append(issues, "密码应包含至少一个小写字母")
	}

	// 检查是否包含大写字母
	hasUpper := false
	for _, char := range password {
		if char >= 'A' && char <= 'Z' {
			hasUpper = true
			break
		}
	}
	if !hasUpper {
		issues = append(issues, "密码应包含至少一个大写字母")
	}

	// 检查是否包含特殊字符
	specialChars := "!@#$%^&*()_+-=[]{}|;:,.<>?"
	hasSpecial := false
	for _, char := range password {
		for _, special := range specialChars {
			if char == special {
				hasSpecial = true
				break
			}
		}
		if hasSpecial {
			break
		}
	}
	if !hasSpecial {
		issues = append(issues, "密码应包含至少一个特殊字符")
	}

	return len(issues) == 0, issues
}

// SetCost 设置bcrypt成本参数
// 在生产环境中，可能需要根据服务器性能调整这个值
func (pm *PasswordManager) SetCost(cost int) error {
	if cost < bcrypt.MinCost || cost > bcrypt.MaxCost {
		return fmt.Errorf("成本参数必须在 %d 到 %d 之间", bcrypt.MinCost, bcrypt.MaxCost)
	}
	pm.cost = cost
	return nil
}

// GetCost 获取当前成本参数
func (pm *PasswordManager) GetCost() int {
	return pm.cost
}
