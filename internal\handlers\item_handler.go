package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"golang-learning-mvp/internal/database"
	"golang-learning-mvp/internal/middleware"
	"golang-learning-mvp/internal/models"
)

// ItemHandler 数据项处理器
type ItemHandler struct {
	itemRepo *database.ItemRepository
}

// NewItemHandler 创建数据项处理器实例
func NewItemHandler(itemRepo *database.ItemRepository) *ItemHandler {
	return &ItemHandler{
		itemRepo: itemRepo,
	}
}

// CreateItem 创建数据项
// @Summary 创建新数据项
// @Description 为当前用户创建新的数据项
// @Tags 数据项
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param item body models.ItemCreateRequest true "数据项信息"
// @Success 201 {object} middleware.SuccessResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Router /api/items [post]
func (h *ItemHandler) CreateItem(c *gin.Context) {
	var req models.ItemCreateRequest
	
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.HandleBadRequestError(c, "请求数据格式错误: "+err.Error())
		return
	}

	// 从中间件获取用户ID
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		middleware.HandleAuthError(c, "无法获取用户信息")
		return
	}

	// 创建数据项模型
	item := &models.Item{
		Title:       req.Title,
		Description: req.Description,
		UserID:      userID,
	}

	// 保存到数据库
	if err := h.itemRepo.CreateItem(item); err != nil {
		middleware.HandleDatabaseError(c, err)
		return
	}

	middleware.HandleCreated(c, "数据项创建成功", item.ToResponse())
}

// GetItems 获取数据项列表
// @Summary 获取用户的数据项列表
// @Description 分页获取当前用户的数据项
// @Tags 数据项
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(10)
// @Param search query string false "搜索关键词"
// @Success 200 {object} middleware.SuccessResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Router /api/items [get]
func (h *ItemHandler) GetItems(c *gin.Context) {
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		middleware.HandleAuthError(c, "无法获取用户信息")
		return
	}

	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")

	// 计算偏移量
	offset := (page - 1) * limit

	var items []*models.Item
	var err error

	// 根据是否有搜索关键词选择查询方法
	if search != "" {
		items, err = h.itemRepo.SearchItems(userID, search, limit, offset)
	} else {
		items, err = h.itemRepo.GetItemsByUserID(userID, limit, offset)
	}

	if err != nil {
		middleware.HandleDatabaseError(c, err)
		return
	}

	// 获取总数
	total, err := h.itemRepo.GetItemsCount(userID)
	if err != nil {
		middleware.HandleDatabaseError(c, err)
		return
	}

	// 转换为响应格式
	var itemResponses []models.ItemResponse
	for _, item := range items {
		itemResponses = append(itemResponses, item.ToResponse())
	}

	middleware.HandleSuccess(c, "获取数据项列表成功", gin.H{
		"items": itemResponses,
		"pagination": gin.H{
			"page":  page,
			"limit": limit,
			"total": total,
		},
	})
}

// GetItem 获取单个数据项
// @Summary 获取数据项详情
// @Description 根据ID获取数据项详情
// @Tags 数据项
// @Produce json
// @Security BearerAuth
// @Param id path int true "数据项ID"
// @Success 200 {object} middleware.SuccessResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Router /api/items/{id} [get]
func (h *ItemHandler) GetItem(c *gin.Context) {
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		middleware.HandleAuthError(c, "无法获取用户信息")
		return
	}

	// 解析ID参数
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		middleware.HandleBadRequestError(c, "无效的数据项ID")
		return
	}

	// 查询数据项
	item, err := h.itemRepo.GetItemByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, middleware.ErrorResponse{
			Error:   "数据项不存在",
			Message: "请求的数据项不存在",
			Code:    404,
		})
		return
	}

	// 检查所有权
	if item.UserID != userID {
		middleware.HandleForbiddenError(c, "您无权访问此数据项")
		return
	}

	middleware.HandleSuccess(c, "获取数据项成功", item.ToResponse())
}

// UpdateItem 更新数据项
// @Summary 更新数据项
// @Description 更新指定的数据项
// @Tags 数据项
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "数据项ID"
// @Param item body models.ItemUpdateRequest true "更新的数据项信息"
// @Success 200 {object} middleware.SuccessResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Router /api/items/{id} [put]
func (h *ItemHandler) UpdateItem(c *gin.Context) {
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		middleware.HandleAuthError(c, "无法获取用户信息")
		return
	}

	// 解析ID参数
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		middleware.HandleBadRequestError(c, "无效的数据项ID")
		return
	}

	var req models.ItemUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.HandleBadRequestError(c, "请求数据格式错误: "+err.Error())
		return
	}

	// 查询现有数据项
	item, err := h.itemRepo.GetItemByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, middleware.ErrorResponse{
			Error:   "数据项不存在",
			Message: "请求的数据项不存在",
			Code:    404,
		})
		return
	}

	// 检查所有权
	if item.UserID != userID {
		middleware.HandleForbiddenError(c, "您无权修改此数据项")
		return
	}

	// 更新字段（只更新非空字段）
	if req.Title != "" {
		item.Title = req.Title
	}
	if req.Description != "" {
		item.Description = req.Description
	}

	// 保存更新
	if err := h.itemRepo.UpdateItem(item); err != nil {
		middleware.HandleDatabaseError(c, err)
		return
	}

	middleware.HandleSuccess(c, "数据项更新成功", item.ToResponse())
}

// DeleteItem 删除数据项
// @Summary 删除数据项
// @Description 删除指定的数据项
// @Tags 数据项
// @Produce json
// @Security BearerAuth
// @Param id path int true "数据项ID"
// @Success 200 {object} middleware.SuccessResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Router /api/items/{id} [delete]
func (h *ItemHandler) DeleteItem(c *gin.Context) {
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		middleware.HandleAuthError(c, "无法获取用户信息")
		return
	}

	// 解析ID参数
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		middleware.HandleBadRequestError(c, "无效的数据项ID")
		return
	}

	// 删除数据项（包含所有权检查）
	if err := h.itemRepo.DeleteItem(id, userID); err != nil {
		if err.Error() == "数据项不存在或无权限删除" {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error:   "数据项不存在",
				Message: "请求的数据项不存在或您无权删除",
				Code:    404,
			})
			return
		}
		middleware.HandleDatabaseError(c, err)
		return
	}

	middleware.HandleSuccess(c, "数据项删除成功", nil)
}
