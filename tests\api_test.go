package tests

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"golang-learning-mvp/internal/auth"
	"golang-learning-mvp/internal/database"
	"golang-learning-mvp/internal/handlers"
	"golang-learning-mvp/internal/middleware"
	"golang-learning-mvp/internal/models"
)

// setupTestRouter 设置测试路由器
func setupTestRouter() *gin.Engine {
	// 设置测试模式
	gin.SetMode(gin.TestMode)

	// 初始化测试数据库
	testDBPath := "./test.db"
	if err := database.InitDatabase(testDBPath); err != nil {
		panic("测试数据库初始化失败: " + err.Error())
	}

	// 创建仓库和处理器
	userRepo := database.NewUserRepository(database.GetDB())
	itemRepo := database.NewItemRepository(database.GetDB())
	jwtManager := auth.NewJWTManager("test-secret", "test-app", 24*time.Hour)
	passwordManager := auth.NewPasswordManager()

	authHandler := handlers.NewAuthHandler(userRepo, jwtManager, passwordManager)
	itemHandler := handlers.NewItemHandler(itemRepo)

	// 创建路由器
	router := gin.New()
	router.Use(middleware.ErrorHandlerMiddleware())

	// 设置路由
	api := router.Group("/api")
	{
		auth := api.Group("/auth")
		{
			auth.POST("/register", authHandler.Register)
			auth.POST("/login", authHandler.Login)
		}

		authenticated := api.Group("")
		authenticated.Use(middleware.AuthMiddleware(jwtManager))
		{
			authenticated.GET("/auth/profile", authHandler.GetProfile)
			
			items := authenticated.Group("/items")
			{
				items.POST("", itemHandler.CreateItem)
				items.GET("", itemHandler.GetItems)
				items.GET("/:id", itemHandler.GetItem)
				items.PUT("/:id", itemHandler.UpdateItem)
				items.DELETE("/:id", itemHandler.DeleteItem)
			}
		}
	}

	return router
}

// cleanupTestDB 清理测试数据库
func cleanupTestDB() {
	database.CloseDatabase()
	os.Remove("./test.db")
}

// TestUserRegistration 测试用户注册
func TestUserRegistration(t *testing.T) {
	router := setupTestRouter()
	defer cleanupTestDB()

	t.Run("成功注册", func(t *testing.T) {
		user := models.UserCreateRequest{
			Username: "testuser",
			Email:    "<EMAIL>",
			Password: "Password123!",
		}

		jsonData, _ := json.Marshal(user)
		req, _ := http.NewRequest("POST", "/api/auth/register", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusCreated {
			t.Errorf("期望状态码 %d, 实际 %d", http.StatusCreated, w.Code)
		}

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)

		if response["message"] != "用户注册成功" {
			t.Errorf("期望消息 '用户注册成功', 实际 '%v'", response["message"])
		}

		// 检查是否返回了token
		data := response["data"].(map[string]interface{})
		if data["token"] == nil {
			t.Error("响应中应该包含token")
		}
	})

	t.Run("重复注册", func(t *testing.T) {
		// 先注册一个用户
		user := models.UserCreateRequest{
			Username: "testuser2",
			Email:    "<EMAIL>",
			Password: "Password123!",
		}

		jsonData, _ := json.Marshal(user)
		req, _ := http.NewRequest("POST", "/api/auth/register", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// 再次注册相同用户
		req2, _ := http.NewRequest("POST", "/api/auth/register", bytes.NewBuffer(jsonData))
		req2.Header.Set("Content-Type", "application/json")

		w2 := httptest.NewRecorder()
		router.ServeHTTP(w2, req2)

		if w2.Code != http.StatusConflict {
			t.Errorf("期望状态码 %d, 实际 %d", http.StatusConflict, w2.Code)
		}
	})

	t.Run("无效数据注册", func(t *testing.T) {
		user := models.UserCreateRequest{
			Username: "ab", // 太短
			Email:    "invalid-email",
			Password: "123", // 太短
		}

		jsonData, _ := json.Marshal(user)
		req, _ := http.NewRequest("POST", "/api/auth/register", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusBadRequest {
			t.Errorf("期望状态码 %d, 实际 %d", http.StatusBadRequest, w.Code)
		}
	})
}

// TestUserLogin 测试用户登录
func TestUserLogin(t *testing.T) {
	router := setupTestRouter()
	defer cleanupTestDB()

	// 先注册一个用户
	registerUser := models.UserCreateRequest{
		Username: "logintest",
		Email:    "<EMAIL>",
		Password: "Password123!",
	}

	jsonData, _ := json.Marshal(registerUser)
	req, _ := http.NewRequest("POST", "/api/auth/register", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	t.Run("成功登录", func(t *testing.T) {
		loginReq := models.UserLoginRequest{
			Username: "logintest",
			Password: "Password123!",
		}

		jsonData, _ := json.Marshal(loginReq)
		req, _ := http.NewRequest("POST", "/api/auth/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Errorf("期望状态码 %d, 实际 %d", http.StatusOK, w.Code)
		}

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)

		if response["message"] != "登录成功" {
			t.Errorf("期望消息 '登录成功', 实际 '%v'", response["message"])
		}
	})

	t.Run("错误密码登录", func(t *testing.T) {
		loginReq := models.UserLoginRequest{
			Username: "logintest",
			Password: "wrongpassword",
		}

		jsonData, _ := json.Marshal(loginReq)
		req, _ := http.NewRequest("POST", "/api/auth/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusUnauthorized {
			t.Errorf("期望状态码 %d, 实际 %d", http.StatusUnauthorized, w.Code)
		}
	})
}

// getAuthToken 获取认证token（辅助函数）
func getAuthToken(router *gin.Engine) string {
	// 注册用户
	user := models.UserCreateRequest{
		Username: "authtest",
		Email:    "<EMAIL>",
		Password: "Password123!",
	}

	jsonData, _ := json.Marshal(user)
	req, _ := http.NewRequest("POST", "/api/auth/register", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)

	data := response["data"].(map[string]interface{})
	return data["token"].(string)
}

// TestItemCRUD 测试数据项CRUD操作
func TestItemCRUD(t *testing.T) {
	router := setupTestRouter()
	defer cleanupTestDB()

	token := getAuthToken(router)

	t.Run("创建数据项", func(t *testing.T) {
		item := models.ItemCreateRequest{
			Title:       "测试数据项",
			Description: "这是一个测试数据项",
		}

		jsonData, _ := json.Marshal(item)
		req, _ := http.NewRequest("POST", "/api/items", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusCreated {
			t.Errorf("期望状态码 %d, 实际 %d", http.StatusCreated, w.Code)
		}
	})

	t.Run("获取数据项列表", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/items", nil)
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Errorf("期望状态码 %d, 实际 %d", http.StatusOK, w.Code)
		}
	})

	t.Run("未认证访问", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/items", nil)
		// 不设置Authorization头

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusUnauthorized {
			t.Errorf("期望状态码 %d, 实际 %d", http.StatusUnauthorized, w.Code)
		}
	})
}
