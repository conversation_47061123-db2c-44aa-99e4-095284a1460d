# 开发环境启动脚本
# 这个脚本用于启动开发环境，包括热重载功能

Write-Host "启动Go后端开发环境..." -ForegroundColor Green

# 检查Air是否安装
if (!(Get-Command "air" -ErrorAction SilentlyContinue)) {
    Write-Host "Air未安装，正在安装..." -ForegroundColor Yellow
    go install github.com/air-verse/air@latest
}

# 创建必要的目录
if (!(Test-Path "data")) {
    New-Item -ItemType Directory -Path "data"
    Write-Host "创建data目录" -ForegroundColor Blue
}

if (!(Test-Path "tmp")) {
    New-Item -ItemType Directory -Path "tmp"
    Write-Host "创建tmp目录" -ForegroundColor Blue
}

# 检查环境变量文件
if (!(Test-Path ".env")) {
    Copy-Item ".env.example" ".env"
    Write-Host "创建.env文件，请根据需要修改配置" -ForegroundColor Yellow
}

Write-Host "启动热重载开发服务器..." -ForegroundColor Green
Write-Host "服务器将在 http://localhost:8080 启动" -ForegroundColor Cyan
Write-Host "按 Ctrl+C 停止服务器" -ForegroundColor Yellow

# 启动Air热重载
air
