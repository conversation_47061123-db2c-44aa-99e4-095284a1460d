package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"golang-learning-mvp/internal/auth"
)

// AuthMiddleware JWT认证中间件
// 这个中间件用于保护需要认证的API端点
func AuthMiddleware(jwtManager *auth.JWTManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取Authorization
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "未授权",
				"message": "缺少Authorization头",
			})
			c.Abort() // 阻止继续执行后续处理器
			return
		}

		// 检查Bearer token格式
		// 标准格式: "Bearer <token>"
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"error":   "未授权",
				"message": "Authorization头格式错误",
			})
			c.Abort()
			return
		}

		// 提取token
		tokenString := tokenParts[1]

		// 验证token
		claims, err := jwtManager.ValidateToken(tokenString)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "未授权",
				"message": "无效的token: " + err.Error(),
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中，供后续处理器使用
		// 这是Gin中传递数据的标准方式
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)

		// 继续执行后续处理器
		c.Next()
	}
}

// OptionalAuthMiddleware 可选认证中间件
// 这个中间件不强制要求认证，但如果提供了有效token会设置用户信息
func OptionalAuthMiddleware(jwtManager *auth.JWTManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			// 没有提供token，继续执行但不设置用户信息
			c.Next()
			return
		}

		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			// token格式错误，继续执行但不设置用户信息
			c.Next()
			return
		}

		tokenString := tokenParts[1]
		claims, err := jwtManager.ValidateToken(tokenString)
		if err != nil {
			// token无效，继续执行但不设置用户信息
			c.Next()
			return
		}

		// 设置用户信息
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Next()
	}
}

// GetUserIDFromContext 从上下文中获取用户ID
// 这是一个辅助函数，用于在处理器中获取当前用户ID
func GetUserIDFromContext(c *gin.Context) (int, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return 0, false
	}
	
	id, ok := userID.(int)
	return id, ok
}

// GetUsernameFromContext 从上下文中获取用户名
func GetUsernameFromContext(c *gin.Context) (string, bool) {
	username, exists := c.Get("username")
	if !exists {
		return "", false
	}
	
	name, ok := username.(string)
	return name, ok
}

// RequireOwnership 要求资源所有权的中间件
// 这个中间件确保用户只能访问自己的资源
func RequireOwnership() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取当前用户ID
		currentUserID, exists := GetUserIDFromContext(c)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "未授权",
				"message": "无法获取用户信息",
			})
			c.Abort()
			return
		}

		// 从URL参数获取资源所有者ID
		// 这里假设URL中有user_id参数
		resourceUserID := c.Param("user_id")
		if resourceUserID == "" {
			// 如果没有user_id参数，跳过检查
			c.Next()
			return
		}

		// 简单的字符串比较（在实际应用中可能需要更复杂的逻辑）
		if resourceUserID != string(rune(currentUserID)) {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "禁止访问",
				"message": "您只能访问自己的资源",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
