package config

import (
	"log"
	"os"
	"strconv"
)

// Config 应用程序配置结构体
// 这个结构体包含了应用程序运行所需的所有配置信息
type Config struct {
	// 服务器配置
	Port string // 服务器监听端口

	// 数据库配置
	DatabasePath string // SQLite数据库文件路径

	// JWT配置
	JWTSecret string // JWT签名密钥

	// 应用配置
	Environment string // 运行环境 (development, production)
}

// LoadConfig 从环境变量加载配置
// 这个函数演示了Go中如何处理环境变量和默认值
func LoadConfig() *Config {
	config := &Config{
		// 使用getEnv函数获取环境变量，如果不存在则使用默认值
		Port:         getEnv("PORT", "8080"),
		DatabasePath: getEnv("DATABASE_PATH", "./data/app.db"),
		JWTSecret:    getEnv("JWT_SECRET", "your-secret-key-change-in-production"),
		Environment:  getEnv("ENVIRONMENT", "development"),
	}

	// 在开发环境下打印配置信息（生产环境不应该打印敏感信息）
	if config.Environment == "development" {
		log.Printf("配置加载完成: Port=%s, DatabasePath=%s, Environment=%s", 
			config.Port, config.DatabasePath, config.Environment)
	}

	return config
}

// getEnv 获取环境变量，如果不存在则返回默认值
// 这是Go中处理环境变量的常见模式
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt 获取环境变量并转换为整数
// 这个函数展示了Go中的错误处理模式
func getEnvAsInt(key string, defaultValue int) int {
	valueStr := getEnv(key, "")
	if value, err := strconv.Atoi(valueStr); err == nil {
		return value
	}
	return defaultValue
}

// getEnvAsBool 获取环境变量并转换为布尔值
func getEnvAsBool(key string, defaultValue bool) bool {
	valueStr := getEnv(key, "")
	if value, err := strconv.ParseBool(valueStr); err == nil {
		return value
	}
	return defaultValue
}
