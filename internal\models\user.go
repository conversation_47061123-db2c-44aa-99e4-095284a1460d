package models

import (
	"time"
)

// User 用户模型
// 这个结构体定义了用户的数据结构，对应数据库中的users表
type User struct {
	ID        int       `json:"id" db:"id"`                           // 用户ID，主键
	Username  string    `json:"username" db:"username"`               // 用户名，唯一
	Email     string    `json:"email" db:"email"`                     // 邮箱，唯一
	Password  string    `json:"-" db:"password"`                      // 密码，json:"-"表示序列化时忽略此字段
	CreatedAt time.Time `json:"created_at" db:"created_at"`           // 创建时间
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`           // 更新时间
}

// UserCreateRequest 用户注册请求结构体
// 这个结构体用于接收前端发送的注册请求数据
type UserCreateRequest struct {
	Username string `json:"username" binding:"required,min=3,max=20"` // 用户名，必填，3-20字符
	Email    string `json:"email" binding:"required,email"`           // 邮箱，必填，需要是有效邮箱格式
	Password string `json:"password" binding:"required,min=6"`        // 密码，必填，最少6字符
}

// UserLoginRequest 用户登录请求结构体
type UserLoginRequest struct {
	Username string `json:"username" binding:"required"` // 用户名，必填
	Password string `json:"password" binding:"required"` // 密码，必填
}

// UserResponse 用户响应结构体
// 这个结构体用于返回给前端的用户信息，不包含敏感信息如密码
type UserResponse struct {
	ID        int       `json:"id"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ToResponse 将User模型转换为UserResponse
// 这是Go中常见的模式，用于数据转换和隐藏敏感信息
func (u *User) ToResponse() UserResponse {
	return UserResponse{
		ID:        u.ID,
		Username:  u.Username,
		Email:     u.Email,
		CreatedAt: u.CreatedAt,
		UpdatedAt: u.UpdatedAt,
	}
}

// Item 示例数据项模型
// 这个模型用于演示基本的CRUD操作
type Item struct {
	ID          int       `json:"id" db:"id"`
	Title       string    `json:"title" db:"title"`
	Description string    `json:"description" db:"description"`
	UserID      int       `json:"user_id" db:"user_id"`         // 外键，关联到用户
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// ItemCreateRequest 创建数据项请求结构体
type ItemCreateRequest struct {
	Title       string `json:"title" binding:"required,min=1,max=100"`       // 标题，必填
	Description string `json:"description" binding:"max=500"`                // 描述，可选，最多500字符
}

// ItemUpdateRequest 更新数据项请求结构体
type ItemUpdateRequest struct {
	Title       string `json:"title" binding:"omitempty,min=1,max=100"`      // 标题，可选更新
	Description string `json:"description" binding:"omitempty,max=500"`      // 描述，可选更新
}

// ItemResponse 数据项响应结构体
type ItemResponse struct {
	ID          int       `json:"id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	UserID      int       `json:"user_id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ToResponse 将Item模型转换为ItemResponse
func (i *Item) ToResponse() ItemResponse {
	return ItemResponse{
		ID:          i.ID,
		Title:       i.Title,
		Description: i.Description,
		UserID:      i.UserID,
		CreatedAt:   i.CreatedAt,
		UpdatedAt:   i.UpdatedAt,
	}
}
