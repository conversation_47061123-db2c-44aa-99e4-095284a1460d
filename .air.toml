# Air配置文件 - Go热重载开发工具
# 这个文件配置了Air工具的行为，实现代码修改后自动重新编译和运行

root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
  # 要监视的文件扩展名
  include_ext = ["go", "tpl", "tmpl", "html"]
  
  # 要排除的文件扩展名
  exclude_ext = ["txt", "md", "log"]
  
  # 要监视的目录
  include_dir = []
  
  # 要排除的目录
  exclude_dir = ["assets", "tmp", "vendor", "testdata", "docs", ".git", ".vscode", ".idea"]
  
  # 要排除的文件
  exclude_file = []
  
  # 要排除的正则表达式
  exclude_regex = ["_test.go"]
  
  # 是否排除未更改的文件
  exclude_unchanged = false
  
  # 是否跟随符号链接
  follow_symlink = false
  
  # 是否启用完整构建标志
  full_bin = ""
  
  # 要执行的命令
  cmd = "go build -o ./tmp/main.exe ./cmd/api"
  
  # 构建后要运行的二进制文件
  bin = "tmp\\main.exe"
  
  # 自定义二进制文件
  full_bin = ""
  
  # 构建参数
  args_bin = []
  
  # 构建日志
  log = "build-errors.log"
  
  # 延迟时间（毫秒）
  delay = 1000
  
  # 停止运行旧的二进制文件时间（毫秒）
  stop_on_root = false
  
  # 发送中断信号前的延迟时间（毫秒）
  send_interrupt = false
  
  # 发送终止信号前的延迟时间（毫秒）
  kill_delay = "0s"

[log]
  # 显示时间
  time = true
  
  # 只显示主要日志
  main_only = false

[color]
  # 自定义每个部分显示的颜色
  main = "magenta"
  watcher = "cyan"
  build = "yellow"
  runner = "green"

[misc]
  # 删除每次构建时的tmp目录
  clean_on_exit = false

[screen]
  # 清屏
  clear_on_rebuild = false
  
  # 保持滚动
  keep_scroll = true
