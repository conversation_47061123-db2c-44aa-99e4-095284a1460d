# 🚀 快速启动指南

## 立即开始

### 1. 启动服务器
```bash
# 方法1：使用开发脚本（推荐）
.\scripts\dev.ps1

# 方法2：直接运行
go run cmd/api/main.go
```

### 2. 测试API
打开浏览器访问：http://localhost:8080/health

### 3. 注册用户
```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>", 
    "password": "Password123!"
  }'
```

### 4. 登录获取Token
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "Password123!"
  }'
```

### 5. 使用Token创建数据
```bash
curl -X POST http://localhost:8080/api/items \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "title": "我的第一个数据项",
    "description": "这是一个测试"
  }'
```

## 🧪 运行测试
```bash
go test ./tests/... -v
```

## 📚 学习资源
- [README.md](./README.md) - 完整项目文档
- [API示例](./docs/api-examples.md) - 详细API使用方法
- [Go学习指南](./docs/go-learning-guide.md) - Go语言学习指导

## 🛠️ 开发工具
- 热重载：使用Air工具自动重启服务器
- 测试：完整的单元测试和API测试
- 日志：详细的请求和错误日志

## 📁 项目结构
```
├── cmd/api/main.go          # 程序入口
├── internal/handlers/       # API处理器
├── internal/auth/          # 认证模块
├── internal/database/      # 数据库层
├── internal/middleware/    # 中间件
├── tests/                  # 测试文件
└── docs/                   # 文档
```

开始你的Go后端学习之旅吧！🎉
