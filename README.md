# Go后端学习项目 - MVP版本

这是一个专为前端开发者设计的Go后端学习项目，提供了完整的RESTful API实现，包含用户认证、数据CRUD操作等核心功能。

## 🎯 项目目标

- 为前端开发者提供Go后端开发的入门项目
- 展示Go语言的核心特性和最佳实践
- 实现一个可用的MVP（最小可行产品）后端服务
- 提供清晰的代码结构和详细的中文注释

## 🏗️ 项目结构

```
golang-learning-project-mvp/
├── cmd/api/                 # 应用程序入口
│   └── main.go             # 主程序文件
├── config/                 # 配置管理
│   └── config.go          # 配置结构和加载逻辑
├── internal/              # 内部包（不对外暴露）
│   ├── auth/              # 认证相关
│   │   ├── jwt.go         # JWT令牌管理
│   │   └── password.go    # 密码哈希和验证
│   ├── database/          # 数据库层
│   │   ├── database.go    # 数据库连接和初始化
│   │   ├── user_repository.go  # 用户数据访问层
│   │   └── item_repository.go  # 数据项访问层
│   ├── handlers/          # HTTP处理器
│   │   ├── auth_handler.go     # 认证相关API
│   │   └── item_handler.go     # 数据项CRUD API
│   ├── middleware/        # 中间件
│   │   ├── auth.go        # 认证中间件
│   │   ├── cors.go        # CORS跨域处理
│   │   ├── logger.go      # 日志中间件
│   │   └── error.go       # 错误处理中间件
│   └── models/            # 数据模型
│       └── user.go        # 用户和数据项模型
├── pkg/utils/             # 公共工具包
├── tests/                 # 测试文件
│   ├── auth_test.go       # 认证功能测试
│   └── api_test.go        # API接口测试
├── scripts/               # 脚本文件
│   └── dev.ps1           # 开发环境启动脚本
├── docs/                  # 文档目录
├── .air.toml             # Air热重载配置
├── .env.example          # 环境变量示例
├── go.mod                # Go模块文件
└── README.md             # 项目说明文档
```

## 🚀 快速开始

### 环境要求

- Go 1.19 或更高版本
- Git

### 安装和运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd golang-learning-project-mvp
   ```

2. **安装依赖**
   ```bash
   go mod tidy
   ```

3. **配置环境变量**
   ```bash
   copy .env.example .env
   # 根据需要修改 .env 文件中的配置
   ```

4. **启动开发服务器**
   ```bash
   # Windows PowerShell
   .\scripts\dev.ps1
   
   # 或者直接运行
   go run cmd/api/main.go
   ```

5. **访问API**
   - 服务器地址: http://localhost:8080
   - 健康检查: http://localhost:8080/health

## 📚 API文档

### 认证相关

#### 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "Password123!"
}
```

#### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "Password123!"
}
```

#### 获取用户资料
```http
GET /api/auth/profile
Authorization: Bearer <your-jwt-token>
```

### 数据项CRUD

#### 创建数据项
```http
POST /api/items
Authorization: Bearer <your-jwt-token>
Content-Type: application/json

{
  "title": "我的数据项",
  "description": "这是一个测试数据项"
}
```

#### 获取数据项列表
```http
GET /api/items?page=1&limit=10&search=关键词
Authorization: Bearer <your-jwt-token>
```

#### 获取单个数据项
```http
GET /api/items/{id}
Authorization: Bearer <your-jwt-token>
```

#### 更新数据项
```http
PUT /api/items/{id}
Authorization: Bearer <your-jwt-token>
Content-Type: application/json

{
  "title": "更新的标题",
  "description": "更新的描述"
}
```

#### 删除数据项
```http
DELETE /api/items/{id}
Authorization: Bearer <your-jwt-token>
```

## 🧪 运行测试

```bash
# 运行所有测试
go test ./tests/...

# 运行特定测试
go test ./tests/ -run TestUserRegistration

# 运行测试并显示覆盖率
go test ./tests/... -cover

# 运行性能测试
go test ./tests/ -bench=.
```

## 🔧 开发工具

### 热重载开发

项目配置了Air工具实现热重载开发：

```bash
# 安装Air
go install github.com/air-verse/air@latest

# 启动热重载开发服务器
air
```

### 代码格式化

```bash
# 格式化代码
go fmt ./...

# 检查代码
go vet ./...
```

## 📖 学习要点

### Go语言特性

1. **包管理**: 了解Go的包系统和模块管理
2. **结构体和方法**: 学习Go的面向对象编程方式
3. **接口**: 理解Go的接口设计哲学
4. **错误处理**: 掌握Go的错误处理模式
5. **并发**: 了解goroutine和channel（在后续扩展中）

### Web开发概念

1. **RESTful API设计**: 学习REST架构风格
2. **中间件模式**: 理解请求处理管道
3. **JWT认证**: 掌握无状态认证机制
4. **数据库操作**: 学习SQL和ORM概念
5. **错误处理**: 统一的错误响应格式

### 项目架构

1. **分层架构**: Handler -> Service -> Repository
2. **依赖注入**: 通过构造函数注入依赖
3. **配置管理**: 环境变量和配置文件
4. **测试驱动**: 单元测试和集成测试

## 🔒 安全特性

- 密码bcrypt哈希加密
- JWT令牌认证
- CORS跨域保护
- SQL注入防护
- 输入数据验证
- 安全响应头

## 🚀 部署建议

### 生产环境配置

1. 修改JWT密钥为强随机字符串
2. 使用PostgreSQL或MySQL替代SQLite
3. 配置HTTPS
4. 设置适当的CORS策略
5. 添加速率限制
6. 配置日志收集

### Docker部署

```dockerfile
# 可以添加Dockerfile进行容器化部署
FROM golang:1.19-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o main cmd/api/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/main .
CMD ["./main"]
```

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📝 许可证

MIT License

## 📞 联系方式

如有问题或建议，请创建Issue或联系项目维护者。

---

**祝你学习愉快！** 🎉
