package database

import (
	"database/sql"
	"fmt"
	"time"

	"golang-learning-mvp/internal/models"
)

// ItemRepository 数据项仓库
// 这个结构体封装了所有与数据项相关的数据库操作
type ItemRepository struct {
	db *sql.DB
}

// NewItemRepository 创建数据项仓库实例
func NewItemRepository(db *sql.DB) *ItemRepository {
	return &ItemRepository{db: db}
}

// CreateItem 创建新数据项
func (r *ItemRepository) CreateItem(item *models.Item) error {
	query := `
		INSERT INTO items (title, description, user_id, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?)
	`
	
	now := time.Now()
	item.CreatedAt = now
	item.UpdatedAt = now

	result, err := r.db.Exec(query, item.Title, item.Description, item.UserID, now, now)
	if err != nil {
		return fmt.Errorf("创建数据项失败: %v", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取数据项ID失败: %v", err)
	}

	item.ID = int(id)
	return nil
}

// GetItemByID 根据ID获取数据项
func (r *ItemRepository) GetItemByID(id int) (*models.Item, error) {
	query := `
		SELECT id, title, description, user_id, created_at, updated_at
		FROM items WHERE id = ?
	`

	item := &models.Item{}
	err := r.db.QueryRow(query, id).Scan(
		&item.ID,
		&item.Title,
		&item.Description,
		&item.UserID,
		&item.CreatedAt,
		&item.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("数据项不存在")
		}
		return nil, fmt.Errorf("查询数据项失败: %v", err)
	}

	return item, nil
}

// GetItemsByUserID 获取用户的所有数据项
func (r *ItemRepository) GetItemsByUserID(userID int, limit, offset int) ([]*models.Item, error) {
	query := `
		SELECT id, title, description, user_id, created_at, updated_at
		FROM items 
		WHERE user_id = ?
		ORDER BY created_at DESC
		LIMIT ? OFFSET ?
	`

	rows, err := r.db.Query(query, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("查询用户数据项失败: %v", err)
	}
	defer rows.Close()

	var items []*models.Item
	for rows.Next() {
		item := &models.Item{}
		err := rows.Scan(
			&item.ID,
			&item.Title,
			&item.Description,
			&item.UserID,
			&item.CreatedAt,
			&item.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描数据项失败: %v", err)
		}
		items = append(items, item)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历数据项失败: %v", err)
	}

	return items, nil
}

// UpdateItem 更新数据项
func (r *ItemRepository) UpdateItem(item *models.Item) error {
	query := `
		UPDATE items 
		SET title = ?, description = ?, updated_at = ?
		WHERE id = ? AND user_id = ?
	`

	item.UpdatedAt = time.Now()
	
	result, err := r.db.Exec(query, item.Title, item.Description, item.UpdatedAt, item.ID, item.UserID)
	if err != nil {
		return fmt.Errorf("更新数据项失败: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("检查更新结果失败: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("数据项不存在或无权限更新")
	}

	return nil
}

// DeleteItem 删除数据项
func (r *ItemRepository) DeleteItem(id, userID int) error {
	query := `DELETE FROM items WHERE id = ? AND user_id = ?`
	
	result, err := r.db.Exec(query, id, userID)
	if err != nil {
		return fmt.Errorf("删除数据项失败: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("检查删除结果失败: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("数据项不存在或无权限删除")
	}

	return nil
}

// GetItemsCount 获取用户数据项总数
func (r *ItemRepository) GetItemsCount(userID int) (int, error) {
	query := `SELECT COUNT(*) FROM items WHERE user_id = ?`
	
	var count int
	err := r.db.QueryRow(query, userID).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("获取数据项总数失败: %v", err)
	}

	return count, nil
}

// SearchItems 搜索数据项
func (r *ItemRepository) SearchItems(userID int, keyword string, limit, offset int) ([]*models.Item, error) {
	query := `
		SELECT id, title, description, user_id, created_at, updated_at
		FROM items 
		WHERE user_id = ? AND (title LIKE ? OR description LIKE ?)
		ORDER BY created_at DESC
		LIMIT ? OFFSET ?
	`

	searchPattern := "%" + keyword + "%"
	rows, err := r.db.Query(query, userID, searchPattern, searchPattern, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("搜索数据项失败: %v", err)
	}
	defer rows.Close()

	var items []*models.Item
	for rows.Next() {
		item := &models.Item{}
		err := rows.Scan(
			&item.ID,
			&item.Title,
			&item.Description,
			&item.UserID,
			&item.CreatedAt,
			&item.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描搜索结果失败: %v", err)
		}
		items = append(items, item)
	}

	return items, nil
}
