package middleware

import (
	"log"
	"net/http"
	"runtime/debug"

	"github.com/gin-gonic/gin"
)

// ErrorResponse 错误响应结构体
type ErrorResponse struct {
	Error   string `json:"error"`             // 错误类型
	Message string `json:"message"`           // 错误消息
	Code    int    `json:"code,omitempty"`    // 错误代码（可选）
	Details string `json:"details,omitempty"` // 错误详情（可选，通常只在开发环境显示）
}

// ErrorHandlerMiddleware 全局错误处理中间件
// 这个中间件捕获并处理所有未处理的错误和panic
func ErrorHandlerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			// 捕获panic
			if err := recover(); err != nil {
				// 记录panic信息和堆栈跟踪
				log.Printf("Panic recovered: %v\n%s", err, debug.Stack())
				
				// 返回500错误
				c.JSON(http.StatusInternalServerError, ErrorResponse{
					Error:   "内部服务器错误",
					Message: "服务器遇到了一个意外错误",
					Code:    500,
				})
				
				c.Abort()
			}
		}()

		c.Next()

		// 检查是否有错误
		if len(c.Errors) > 0 {
			// 获取最后一个错误
			err := c.Errors.Last()
			
			// 记录错误
			log.Printf("Request error: %v", err.Error())
			
			// 根据错误类型返回适当的HTTP状态码
			statusCode := http.StatusInternalServerError
			errorType := "内部服务器错误"
			
			// 这里可以根据错误类型进行更精细的处理
			switch err.Type {
			case gin.ErrorTypeBind:
				statusCode = http.StatusBadRequest
				errorType = "请求参数错误"
			case gin.ErrorTypePublic:
				statusCode = http.StatusBadRequest
				errorType = "请求错误"
			}

			c.JSON(statusCode, ErrorResponse{
				Error:   errorType,
				Message: err.Error(),
				Code:    statusCode,
			})
		}
	}
}

// ValidationErrorMiddleware 验证错误处理中间件
// 这个中间件专门处理数据验证错误
func ValidationErrorMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 检查绑定错误
		if len(c.Errors) > 0 {
			err := c.Errors.Last()
			
			if err.Type == gin.ErrorTypeBind {
				c.JSON(http.StatusBadRequest, ErrorResponse{
					Error:   "数据验证失败",
					Message: "请检查请求数据格式和必填字段",
					Details: err.Error(),
					Code:    400,
				})
				return
			}
		}
	}
}

// NotFoundHandler 404错误处理器
func NotFoundHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "资源未找到",
			Message: "请求的资源不存在",
			Code:    404,
		})
	}
}

// MethodNotAllowedHandler 405错误处理器
func MethodNotAllowedHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.JSON(http.StatusMethodNotAllowed, ErrorResponse{
			Error:   "方法不被允许",
			Message: "该资源不支持此HTTP方法",
			Code:    405,
		})
	}
}

// RateLimitErrorHandler 限流错误处理器
func RateLimitErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.JSON(http.StatusTooManyRequests, ErrorResponse{
			Error:   "请求过于频繁",
			Message: "您的请求过于频繁，请稍后再试",
			Code:    429,
		})
	}
}

// DatabaseErrorHandler 数据库错误处理器
// 这个函数用于统一处理数据库相关错误
func HandleDatabaseError(c *gin.Context, err error) {
	log.Printf("Database error: %v", err)
	
	// 在生产环境中，不应该暴露具体的数据库错误信息
	c.JSON(http.StatusInternalServerError, ErrorResponse{
		Error:   "数据库错误",
		Message: "数据操作失败，请稍后重试",
		Code:    500,
	})
}

// AuthErrorHandler 认证错误处理器
func HandleAuthError(c *gin.Context, message string) {
	c.JSON(http.StatusUnauthorized, ErrorResponse{
		Error:   "认证失败",
		Message: message,
		Code:    401,
	})
}

// ForbiddenErrorHandler 权限错误处理器
func HandleForbiddenError(c *gin.Context, message string) {
	c.JSON(http.StatusForbidden, ErrorResponse{
		Error:   "权限不足",
		Message: message,
		Code:    403,
	})
}

// BadRequestErrorHandler 请求错误处理器
func HandleBadRequestError(c *gin.Context, message string) {
	c.JSON(http.StatusBadRequest, ErrorResponse{
		Error:   "请求错误",
		Message: message,
		Code:    400,
	})
}

// SuccessResponse 成功响应结构体
type SuccessResponse struct {
	Message string      `json:"message"`           // 成功消息
	Data    interface{} `json:"data,omitempty"`    // 响应数据
	Code    int         `json:"code,omitempty"`    // 状态代码
}

// HandleSuccess 成功响应处理器
func HandleSuccess(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, SuccessResponse{
		Message: message,
		Data:    data,
		Code:    200,
	})
}

// HandleCreated 创建成功响应处理器
func HandleCreated(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusCreated, SuccessResponse{
		Message: message,
		Data:    data,
		Code:    201,
	})
}
